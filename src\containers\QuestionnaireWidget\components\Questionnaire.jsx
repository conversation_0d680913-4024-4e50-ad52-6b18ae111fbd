import React, { useState, useEffect, useMemo } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { Box } from '@mui/material';
import * as QuestionnaireUtility from '../questionnaireUtility';
import * as CommonUtility from '@/containers/commonUtility';
import { GET_ORG_REQUEST_QUERY_KEY, IDENTIFIED, UNIDENTIFIED, actions } from '@/containers/commonConstants';
import { fetchQuestionnaireQuestions } from '@/redux/actions/getQuestionnaireDetails';
import { QuestionnaireV2 as QuestionnaireComponentV2, Action } from '@/components';
import { Loader } from '@/components';
import useNotification from '@/hooks/useNotification';
import { useTranslation } from 'react-i18next';
import { v4 as uuidv4 } from 'uuid';
import { useAPIs } from '../useAPIs';
import { useSession } from 'next-auth/react';
import { useCommonAPIs } from '@/hooks/useCommonAPIs';
import { useQueryClient } from '@tanstack/react-query';

const questionnaireResponseIdPlaceholder = uuidv4();

export const Questionnaire = (props) => {
  console.log('TRACE: Questionnaire');
  const { questionnaireWidgetSuccessData, handleNavigationCallback, demographic, setIsSignInDialogOpen } = props;

  const { t } = useTranslation();
  const dispatch = useDispatch();
  const { data: authSession } = useSession();
  const { getClientId, getPatientId } = useCommonAPIs();
  const queryClient = useQueryClient();
  const { saveQuestionnaireResponseAtIndividual, saveQuestionnaireResponseAtOrganization, createIndividualRequest } =
    useAPIs();

  const [organizationId, widgetId] = CommonUtility.getOrganizationAndWidgetId();

  const isPreviewWidget = widgetId === '1';
  const dynamicQuestionnaireId = CommonUtility.getParamFromUrl('qid');
  const repositoryQuery = CommonUtility.getParamFromUrl('repository')?.toUpperCase() || '';

  const [pdfTemplate, setPdfTemplate] = useState({});
  const [fhirQuestionnaireResponse, setFhirQuestionnaireResponse] = useState({});
  const [isQuestionnaireFinished, setIsQuestionnaireFinished] = useState(false);
  const [, sendNotification] = useNotification();
  const [isSavedForLaterPending, setIsSaveForLaterPending] = useState(false);
  const [isSavedForLater, setIsSavedForLater] = useState(false);
  const [isActionTriggered, setIsActionTriggered] = useState(false);
  const [isHeadingDescriptionAction, setIsHeadingDescriptionAction] = useState(false);
  const [finished] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  //get questionnaire
  const {
    isQuestionnaireQuestionsFetching,
    isQuestionnaireQuestionsSuccess,
    isQuestionnaireQuestionsError,
    questionnaireQuestionsErrorData,
    questionnaireQuestionsSuccessData,
  } = useSelector((state) => state.getQuestionnaireQuestionRedux);

  const {
    isSaveQuestionnaireResponseOrganizationFetching,
    isSaveQuestionnaireResponseOrganizationSuccess,
    isSaveQuestionnaireResponseOrganizationError,
  } = useSelector((state) => state.saveQuestionnaireResponseOrganizationReducer);

  const {
    isSaveQuestionnaireResponseIndividualFetching,
    isSaveQuestionnaireResponseIndividualSuccess,
    isSaveQuestionnaireResponseIndividualError,
  } = useSelector((state) => state.saveQuestionnaireResponseIndividualReducer);

  const {
    dynamicWidget,
    identification,
    showSignIn,
    nextButtonText,
    previousButtonText,
    doneButtonText,
    showProgressBar,
    showProgressPercentage,
    action,
    questionnaire,
    repository,
    report,
    headingAndDescription,
    saveResponseInOrg,
  } = questionnaireWidgetSuccessData || {};

  const matchedAction = useMemo(() => {
    const instrumentScores = QuestionnaireUtility.extractInstrumentScores(fhirQuestionnaireResponse);
    const matchedAction = QuestionnaireUtility.getMatchedActionCondition(
      action?.metaData?.actionConditions,
      instrumentScores,
    );

    if (!matchedAction) {
      const defaultAction = action.metaData?.actionConditions?.find((condition) => condition.default);
      return defaultAction;
    }

    return matchedAction;
  }, [fhirQuestionnaireResponse, action]);

  let questionnaireId;
  if (isPreviewWidget && dynamicQuestionnaireId) {
    questionnaireId = dynamicQuestionnaireId;
  } else if (dynamicWidget && dynamicQuestionnaireId) {
    questionnaireId = dynamicQuestionnaireId;
  } else {
    questionnaireId = QuestionnaireUtility.getQuestionnaireId(questionnaire);
  }

  useEffect(() => {
    if (questionnaireId && !questionnaireQuestionsSuccessData) {
      dispatch(
        fetchQuestionnaireQuestions({
          questionnaireId,
          repository: isPreviewWidget ? repositoryQuery : repository,
          organizationId,
        }),
      );
    }
  }, [questionnaireId, questionnaireQuestionsSuccessData]);

  useEffect(() => {
    if (isQuestionnaireQuestionsSuccess && questionnaireQuestionsSuccessData) {
      const { pdfTemplate } = questionnaireQuestionsSuccessData;
      setPdfTemplate(pdfTemplate);
    }

    if (isQuestionnaireQuestionsError) {
      console.error('Error fetching questionnaire details:', questionnaireQuestionsErrorData);
    }
  }, [
    isQuestionnaireQuestionsSuccess,
    isQuestionnaireQuestionsError,
    questionnaireQuestionsSuccessData,
    questionnaireQuestionsErrorData,
  ]);

  const executeScroll = () => {
    CommonUtility.scrollToTop();
  };

  const questionnaireSaveCallback = (
    fhirQuestionnaire,
    fhirQuestionnaireResponse,
    responses,
    isFinished,
    isCancelled,
    isSavedForLater,
  ) => {
    const pdfExtension = {
      url: 'pdftemplate-base64',
      valueBase64Binary: pdfTemplate,
    };

    const updatedFhirQuestionnaire = {
      ...fhirQuestionnaire,
      extension: [...fhirQuestionnaire.extension, pdfExtension],
    };

    let questionnaireResponse =
      typeof fhirQuestionnaireResponse === 'object' ? fhirQuestionnaireResponse : JSON.parse(fhirQuestionnaireResponse);

    const clientId = getClientId();

    if (questionnaireResponse) {
      const patientId = getPatientId();
      questionnaireResponse.id = questionnaireResponseIdPlaceholder;
      if (patientId) {
        questionnaireResponse.author = { reference: `Patient/${patientId}` };
        questionnaireResponse.subject = { reference: `Patient/${patientId}` };
        questionnaireResponse.source = { reference: `Patient/${patientId}` };
      }

      if (fhirQuestionnaire.url) {
        questionnaireResponse.questionnaire = fhirQuestionnaire.url;
      }
    }

    const fhirQuestionnaireResponseUpdated = {
      questionnaire: updatedFhirQuestionnaire,
      questionnaireResponse: questionnaireResponse,
    };

    executeScroll();
    console.log('fhirQuestionnaireResponseUpdated ----', fhirQuestionnaireResponseUpdated);
    setIsQuestionnaireFinished(isFinished);

    if (isFinished) {
      setFhirQuestionnaireResponse(questionnaireResponse);
      if (identification !== UNIDENTIFIED && clientId && saveResponseInOrg) {
        saveQuestionnaireResponseAtOrganization(fhirQuestionnaireResponseUpdated);
      }
      if (authSession?.user?.cambianId) {
        saveQuestionnaireResponseAtIndividual(questionnaireResponse);
      }
      if ((identification === UNIDENTIFIED && !authSession?.user?.cambianId) || !clientId) {
        // * used setTimeout to let scrollIntoView (from cambianreact) call complete to scroll to the top
        setTimeout(
          () => handleNavigationCallback(actions.NEXT, { isQuestionnaireFinished: isFinished }, questionnaireResponse),
          2,
        );
        if (!report?.enabled && !headingAndDescription?.enabled && action?.enabled) {
          setIsActionTriggered(true);
        }
      }
    } else if (isSavedForLater) {
      setFhirQuestionnaireResponse(questionnaireResponse);
      setIsSavedForLater(true);
      if (authSession?.user?.cambianId) {
        saveQuestionnaireResponseAtIndividual(questionnaireResponse);
      } else {
        setIsSaveForLaterPending(true);
        handleSignInDialogState(true);
      }
    } else if (isCancelled) {
      handleNavigationCallback(actions.NEXT, { isCancelled }, questionnaireResponse);
    }
  };

  const savePageTransitionCallback = (
    questionnaireId,
    questionnaireResponseId,
    fhirQuestionnaireResponse,
    questionMap,
    transitionResolve,
    transitionReject,
  ) => {
    let questionnaireResponse =
      typeof fhirQuestionnaireResponse === 'object' ? fhirQuestionnaireResponse : JSON.parse(fhirQuestionnaireResponse);
    questionnaireResponse.id = questionnaireResponseIdPlaceholder;
    executeScroll();
  };

  useEffect(() => {
    if (authSession?.user?.cambianId && isSavedForLaterPending) {
      saveQuestionnaireResponseAtIndividual(fhirQuestionnaireResponse);
      setIsSaveForLaterPending(false);
    }
  }, [authSession, isSavedForLaterPending]);

  useEffect(() => {
    if (isSaveQuestionnaireResponseOrganizationSuccess) {
      if (!report?.enabled && !headingAndDescription?.enabled && action?.enabled) {
        setIsActionTriggered(true);
      }
      handleNavigationCallback(actions.NEXT, { isQuestionnaireFinished }, fhirQuestionnaireResponse);
    } else if (isSaveQuestionnaireResponseOrganizationError) {
      sendNotification({ variant: 'error', msg: t('apiError') });
    }
  }, [isSaveQuestionnaireResponseOrganizationSuccess, isSaveQuestionnaireResponseOrganizationError]);

  useEffect(() => {
    if (isSaveQuestionnaireResponseIndividualSuccess) {
      setIsSaveForLaterPending(false);
      (async () => {
        if (isSavedForLater) {
          const orgRequestData = await queryClient.getQueryData(GET_ORG_REQUEST_QUERY_KEY);
          const orgRequest = orgRequestData?.data || {};

          setIsLoading(true);
          await createIndividualRequest(questionnaireResponseIdPlaceholder, orgRequest);
          setIsLoading(false);
        }

        if (!report?.enabled && !headingAndDescription?.enabled && action?.enabled) {
          setIsActionTriggered(true);
        }
        handleNavigationCallback(actions.NEXT, { isSavedForLater }, fhirQuestionnaireResponse);
      })();
    } else if (isSaveQuestionnaireResponseIndividualError) {
      sendNotification({ variant: 'error', msg: t('apiError') });
    }
  }, [isSaveQuestionnaireResponseIndividualSuccess, isSaveQuestionnaireResponseIndividualError]);

  const savePageTransitionCallbackError = () => {
    console.log('Error occurred while saving page response');
  };

  const handleBackNavigation = () => {
    handleNavigationCallback(actions.PREVIOUS);
  };

  const getQuestionnaireComponent = () => {
    return (
      <Box sx={{ pointerEvents: finished && 'none' }}>
        <QuestionnaireComponentV2
          fhirQuestionnaire={questionnaireQuestionsSuccessData?.questionnaire}
          isSaveForLaterActive={showSignIn}
          enableCancelConfirmation={showSignIn}
          isStatusEnabled={showProgressBar}
          isProgressPercentageEnabled={showProgressPercentage}
          nextButtonLabelOverride={nextButtonText || undefined}
          previousButtonLabelOverride={previousButtonText || undefined}
          submitButtonLabelOverride={doneButtonText || undefined}
          questionnaireCallback={questionnaireSaveCallback}
          pageTransitionCallback={savePageTransitionCallback}
          pageErrorTransitionCallback={savePageTransitionCallbackError}
          handleBackNavigation={identification === IDENTIFIED && handleBackNavigation}
        />
      </Box>
    );
  };

  const handleActionNavigation = () => {
    setIsActionTriggered(false);
  };

  const handleSignInDialogState = (state, isSignInCancelled) => {
    setIsSignInDialogOpen(state);
    if (isSignInCancelled) {
      setIsSaveForLaterPending(false);
    }
  };

  var a;
  const handleAutofill = () => {
    a = setInterval(() => {
      console.count('hello world');
      let radioElement = document.querySelectorAll('input[name=radio-buttons-group]')[2];
      if (radioElement) {
        radioElement.click();
      }
      document.querySelectorAll('button').forEach((btn) => btn.innerText === 'Next' && btn.click());
    }, 1);
  };

  return (
    <>
      <Loader
        active={
          isQuestionnaireQuestionsFetching ||
          isSaveQuestionnaireResponseIndividualFetching ||
          isSaveQuestionnaireResponseOrganizationFetching ||
          isLoading
        }
      />

      {process.env.NODE_ENV === 'development' && (
        <>
          <button onClick={handleAutofill}>autofill questionnaire</button>
          <button onClick={() => clearInterval(a)}>stop script</button>
        </>
      )}
      {!isHeadingDescriptionAction && (
        <Box sx={{ px: '4%', py: '16px' }}>
          <Box sx={{ mt: 1 }}>
            {isQuestionnaireQuestionsSuccess ? (
              getQuestionnaireComponent()
            ) : isQuestionnaireQuestionsError ? (
              <>Something went wrong. Please try again later</>
            ) : (
              <></>
            )}
          </Box>
        </Box>
      )}
      {isActionTriggered && (
        <Action
          matchedAction={matchedAction}
          questionnaireResponse={fhirQuestionnaireResponse}
          handleNavigationCallback={(action) => handleActionNavigation(action)}
          setIsHeadingDescriptionAction={setIsHeadingDescriptionAction}
          // actionRule={action?.metaData?.actionConditions}
          demographic={demographic}
          setIsActionPerformed={() => {}}
        />
      )}
    </>
  );
};
